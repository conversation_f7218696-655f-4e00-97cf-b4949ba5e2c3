// 商品详情页
const util = require('@/utils/util.js')
const app = getApp()
const goodsApi = require('@/api/goods.js')
const commApi = require('@/api/commApi.js')

Page({
  data: {
    darkMode: false,
    id: null,
    goods: null,
    userPoints: 0, // 用户当前总积分
    displayMaxDeductiblePoints: 0, // 计算后，最多可用于抵扣的积分数
    displayDeductibleAmount: '0.00', // displayMaxDeductiblePoints 对应的抵扣金额
    estimatedRewardPoints: 0, // 预计可获得的积分
    showContactModal: false,
    showBuyModal: false,
    buyQuantity: 1,
    buyRemark: '',
    totalPrice: '0.00', // 弹窗中的总价
    usePointsForPurchase: false, // 新增：是否在购买弹窗中使用积分
    apiUrl: '', // 图片访问路径

    // ... 其他原有 data 字段
  },

  onLoad: function(options) {
    // 初始化图片访问路径
    this.setData({
      apiUrl: wx.getStorageSync('apiUrl') + '/common-api/v1/file/'
    });

    if (options.id) {
      const goodsId = parseInt(options.id);
      this.setData({
        id: goodsId
      });

      // 从接口获取商品详情
      this.loadGoodsDetail(goodsId);

      // 加载用户积分
      this.loadUserPoints();
    } else {
      // 如果没有商品ID，显示错误信息并返回上一页
      wx.showToast({
        title: '商品ID不存在',
        icon: 'none',
        duration: 2000
      });
      setTimeout(() => {
        wx.navigateBack({
          delta: 1
        });
      }, 2000);
    }
  },

  onShow: function() {
    // 检查暗黑模式
    this.setData({
      darkMode: app.globalData.darkMode || false
    })

    // 监听暗黑模式变化
    if (app.globalData.darkModeChangeEvent) {
      this.setData({
        darkMode: app.globalData.darkModeChangeEvent.darkMode
      })
    }

    // 检查是否有积分变动事件
    if (app.globalData && app.globalData.pointsChangeEvent) {
      console.log('商品详情页检测到积分变动事件:', app.globalData.pointsChangeEvent);
      // 重新加载用户积分，积分变动会自动触发 updatePointsDisplay
      this.loadUserPoints();
      // 清除事件，避免重复处理
      app.globalData.pointsChangeEvent = null;
    }
  },

  // 从接口加载商品详情
  loadGoodsDetail: function(goodsId) {
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    // 使用接口获取商品详情
    goodsApi.getPlatformGoodsDetail(goodsId).then(res => {
      if (res) {
        const goodsData = res;
 
        // 数据格式化处理，根据实际API返回的字段
        const goods = {
          id: goodsData.id,
          isLike:goodsData.isLike,
          like:goodsData.like,
          title: goodsData.title || '',
          stuffDescribe: goodsData.stuffDescribe || '',
          amount: parseFloat(goodsData.amount) || 0,
          type: goodsData.type || 'free', // 使用原始字典值
          stock: goodsData.stock || 0,
          sold: goodsData.sold || 0,
          views: goodsData.views || 0,
          sellerAvatarUrl:goodsData.sellerAvatarUrl,
          media: goodsData.media || '', // 保持原始media字段
          categoryCode: goodsData.categoryCode || '',
          address: goodsData.address || '',
          createTime: goodsData.createTime,
          updateTime: goodsData.updateTime,
          status: goodsData.status || 'pending',
          isMy: goodsData.isMy || false,
          isCollect: goodsData.isCollect || false,
          points: goodsData.points || 0,
          // 用户信息（如果有的话）
          userName: goodsData.userName || '用户',
          memberAvatar: goodsData.memberAvatar || '',
          memberId: goodsData.memberId || '',
          // 积分相关配置
          pointsReward: goodsData.points || 1,
          pointsDiscount: {
            enabled: goodsData.type !== 'free', // 非免费商品才能使用积分抵扣
            ratio: 100, // 100积分=1元
            maxRatio: 0.3 // 最多抵扣30%
          }
        };

        // 处理图片数组
        if (goods.media) {
          goods.images = goods.media.split(',').map(image => {
            const trimmedImage = image.trim();
            if (trimmedImage && !trimmedImage.startsWith('http')) {
              return this.data.apiUrl + trimmedImage;
            }
            return trimmedImage;
          });
        } else {
          goods.images = [];
        }

        // 获取类型和分类的显示名称
        this.loadGoodsTypeAndCategory(goods);

        this.setData({
          goods,
          totalPrice: (parseFloat(goods.amount) || 0).toFixed(2)
        });

        // 触发积分显示更新
        this.updatePointsDisplay();
        wx.hideLoading();
      } else {
        throw new Error(res.message || '获取商品详情失败');
      }
    }).catch(err => {
      wx.hideLoading();

    

      // 延迟返回上一页
      setTimeout(() => {
        wx.navigateBack({
          delta: 1
        });
      }, 2000);
    });
  },

  // 获取商品类型、分类和状态的显示名称
  loadGoodsTypeAndCategory: function(goods) {
    try {
      // 使用统一的字典获取方法
      const typeDict = util.getDictByNameEn('good_stuff_type');
      const categoryDict = util.getDictByNameEn('good_stuff_category');
      const statusDict = util.getDictByNameEn('good_stuff_status');

      // 处理商品类型
      if (typeDict && typeDict.length > 0 && typeDict[0].children) {
        const typeOption = typeDict[0].children.find(option => option.nameEn === goods.type);
        goods.typeName = typeOption ? typeOption.nameCn : goods.type;
        goods.isFreeType = this.checkIsFreeType(goods.type, typeDict[0].children);
      }

      // 处理商品分类
      if (categoryDict && categoryDict.length > 0 && categoryDict[0].children) {
        const categoryOption = categoryDict[0].children.find(option => option.nameEn === goods.categoryCode);
        goods.categoryName = categoryOption ? categoryOption.nameCn : goods.categoryCode;
      }

      // 处理商品状态
      if (statusDict && statusDict.length > 0 && statusDict[0].children) {
        const statusOption = statusDict[0].children.find(option => option.nameEn === goods.status);
        goods.statusName = statusOption ? statusOption.nameCn : goods.status;
      }

      this.setData({ goods });
      console.log('商品字典数据加载完成:', goods);
    } catch (error) {
      console.error('加载商品字典数据失败:', error);
    }
  },

  // 加载用户积分
  loadUserPoints: function() {
    const userInfo = wx.getStorageSync('userInfo');
    const points = (userInfo && typeof userInfo.points === 'number') ? userInfo.points : 0;
    this.setData({
      userPoints: points
    });
    // 触发积分显示更新
    this.updatePointsDisplay();
  },

  // 计算并更新积分相关显示信息
  updatePointsDisplay: function() {
    const { goods, userPoints } = this.data;
    let calculatedMaxDeductiblePoints = 0;
    let calculatedDeductibleAmount = 0;
    let calculatedRewardPoints = 0;

    // Basic validation
    if (!goods || typeof goods.amount === 'undefined' || goods.amount === null) {
      this.setData({
        displayMaxDeductiblePoints: 0,
        displayDeductibleAmount: '0.00',
        estimatedRewardPoints: 0
      });
      return;
    }

    const goodsPrice = parseFloat(goods.amount || 0);
    const currentUserPoints = parseInt(userPoints || 0);

    // --- 计算积分抵扣 ---
    // 检查商品类型是否为付费商品，并且启用了积分抵扣
    const pointsDiscountConfig = goods.pointsDiscount;

    if (goods.type !== 'free' && pointsDiscountConfig && pointsDiscountConfig.enabled && goodsPrice > 0) {
      // 从商品数据中获取积分规则，提供默认值以防万一
      const pointsRatio = parseInt(pointsDiscountConfig.ratio) || 100; // Default 100 pts = 1 yuan
      const maxDiscountRatio = parseFloat(pointsDiscountConfig.maxRatio) || 0.3; // Default 30%

      if (pointsRatio > 0 && maxDiscountRatio > 0 && goodsPrice > 0 && currentUserPoints > 0) {
        // 1. 按商品价格和最大抵扣比例计算最多可抵扣多少金额
        const maxDeductibleAmountByPrice = goodsPrice * maxDiscountRatio;
        // 2. 将此金额换算成需要多少积分
        const pointsNeededForMaxDeduct = Math.floor(maxDeductibleAmountByPrice * pointsRatio);
        // 3. 用户最多能使用的积分是：用户持有积分 和 按规则计算出的最多所需积分 中的较小值
        calculatedMaxDeductiblePoints = Math.min(currentUserPoints, pointsNeededForMaxDeduct);
        // 4. 实际抵扣金额是：用户实际能使用的积分 / 积分比例
        calculatedDeductibleAmount = calculatedMaxDeductiblePoints / pointsRatio;

        // Ensure points used isn't negative or lead to negative amount
        calculatedMaxDeductiblePoints = Math.max(0, calculatedMaxDeductiblePoints);
        calculatedDeductibleAmount = calculatedMaxDeductiblePoints / pointsRatio;
        // Ensure amount isn't negative
        calculatedDeductibleAmount = Math.max(0, calculatedDeductibleAmount);
      }
    }

    // --- 计算积分奖励 ---
    if (goods.type !== 'free' && goodsPrice > 0) {
      // 根据商品设置的积分奖励计算
      calculatedRewardPoints = goods.pointsReward || Math.floor(goodsPrice);
    }

    // 获取最大折扣比例
    const maxDiscountRatio = (pointsDiscountConfig && pointsDiscountConfig.maxRatio) || 0.3;

    // 更新页面数据
    this.setData({
      // 取整显示积分
      displayMaxDeductiblePoints: Math.floor(calculatedMaxDeductiblePoints),
      // 保留两位小数显示金额
      displayDeductibleAmount: calculatedDeductibleAmount.toFixed(2),
      // 取整显示积分
      estimatedRewardPoints: Math.floor(calculatedRewardPoints),
      // 同时更新购买弹窗中使用的变量
      maxCanUsePoints: Math.floor(calculatedMaxDeductiblePoints),
      pointDiscount: calculatedDeductibleAmount.toFixed(2),
      pointsRatio: maxDiscountRatio * 100 // 转换为百分比
    });



    // 如果购买弹窗已打开，更新弹窗内的价格
    if (this.data.showBuyModal) {
      this.calculateTotalPrice();
    }
  },



  onShareAppMessage: function() {
    const goods = this.data.goods
    if (!goods) {
      return {
        title: '商品详情',
        path: '/pages/goods/goods'
      }
    }

    return {
      title: goods.stuffDescribe || '商品详情',
      path: `/profilePackage/pages/goods/detail/detail?id=${goods.id}`,
      imageUrl: (goods.images && goods.images.length > 0) ? goods.images[0] : '/images/default-goods.png'
    }
  },

  onShareTimeline: function() {
    const goods = this.data.goods
    if (!goods) {
      return {
        title: '商品详情'
      }
    }

    return {
      title: goods.stuffDescribe || '商品详情',
      query: `id=${goods.id}`,
      imageUrl: (goods.images && goods.images.length > 0) ? goods.images[0] : '/images/default-goods.png'
    }
  },

  // 联系卖家
  contactSeller: function() {
    // 检查商品数据是否存在
    if (!this.data.goods) {
      wx.showToast({
        title: '商品信息加载失败',
        icon: 'none'
      });
      return;
    }

    // 获取商品和卖家信息
    const goods = this.data.goods;
    const sellerId = goods.userId || '';
    const sellerName = goods.userName || '卖家';
    const goodsId = goods.id || '';
    const goodsTitle = goods.title || '';
    const goodsImage = goods.images && goods.images.length > 0 ? goods.images[0] : '';
    const goodsAmount = goods.amount ;
    
    // 直接跳转到聊天页面
    wx.navigateTo({
      url: `/servicePackage/pages/messages/chat?targetId=${sellerId}}&price=${goodsAmount}&targetName=${sellerName}&goodsId=${goodsId}&goodsTitle=${encodeURIComponent(goodsTitle)}&targetAvatar=${encodeURIComponent(goodsImage)}&goodsImage=${encodeURIComponent(goodsImage)}`
    });
  },

  // 计算最大可用积分
  calculateMaxPoints: function() {
    const { goods, userPoints } = this.data;

    if (!goods || goods.type === 'free' || !goods.pointsDiscount || !goods.pointsDiscount.enabled) {
      this.setData({
        maxCanUsePoints: 0,
        pointDiscount: '0.00'
      });
      return;
    }

    // 直接从商品数据计算最大可用积分
    const ratio = goods.pointsDiscount.ratio || 100; // 默认100积分=1元
    const maxRatio = goods.pointsDiscount.maxRatio || 0.3; // 默认最多抵扣30%
    const totalPrice = parseFloat(goods.amount) * this.data.buyQuantity;
    const maxDiscountAmount = totalPrice * maxRatio;
    const maxPoints = Math.floor(maxDiscountAmount * ratio);

    // 确保不超过用户拥有的积分
    const usedPoints = Math.min(maxPoints, userPoints);

    this.setData({
      maxCanUsePoints: usedPoints,
      pointDiscount: (usedPoints / ratio).toFixed(2),
      pointsRatio: maxRatio * 100, // 转换为百分比
      usePoints: 0 // 初始化为0
    });
  },

  // 立即购买
  buyGoods: function() {
    const { goods } = this.data;

    // 增强的库存检查 (检查商品是否存在，库存是否为0或不足)
    if (!goods) {
      wx.showToast({
        title: '商品信息加载失败',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    if (goods.stock <= 0) {
      wx.showToast({
        title: '商品已售罄',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 检查是否已认证
    if (!util.checkAuthentication()) {
      return;
    }

 

    // 重置购买数量、积分使用和备注
    this.setData({
      buyQuantity: 1,
      buyRemark: '',
      usePoints: 0
    });

    // 重新计算积分抵扣上限（仅付费商品）
    if (goods.type !== 'free' && goods.pointsDiscount && goods.pointsDiscount.enabled) {
      this.calculateMaxPoints();
    }

    // 重新计算总价
    this.calculateTotalPrice();

    // 显示购买确认弹窗
    this.setData({
      showBuyModal: true
    });
  },

  // 切换收藏状态
  toggleFavorite: function() {
    // 检查商品数据是否存在
    if (!this.data.goods) {
      wx.showToast({
        title: '商品信息加载失败',
        icon: 'none'
      });
      return;
    }

    // 检查是否已认证
    if (!util.checkAuthentication()) {
      return;
    }

    const isCollect = !this.data.goods.isCollect;

    if (isCollect) {
      // 收藏商品
      goodsApi.collectGoods(this.data.goods.id).then(res => {
       
          this.setData({
            'goods.isCollect': true,
       
          });
          wx.showToast({
            title: '收藏成功',
            icon: 'success'
          });
      
      }).catch(err => {
      
      });
    } else {
       
      // 取消收藏 - 需要收藏记录ID，这里暂时使用商品ID
      goodsApi.uncollectGoods(this.data.goods.id).then(res => {
      
          this.setData({
            'goods.isCollect': false,
          
          });
          wx.showToast({
            title: '取消收藏成功',
            icon: 'success'
          });
     
      }).catch(err => {
   
      });
    }
  },

  // 切换点赞状态
  toggleLike: function() {
    // 检查商品数据是否存在
    if (!this.data.goods) {
      wx.showToast({
        title: '商品信息加载失败',
        icon: 'none'
      });
      return;
    }

    // 检查是否已认证
    if (!util.checkAuthentication()) {
      return;
    }

    // 点赞功能暂时不实现，显示提示
    // wx.showToast({
    //   title: '点赞功能暂未开放',
    //   icon: 'none'
    // });
     
    if(!this.data.goods.isLike)
    {
      var params={
        goodStuffId:this.data.goods.id
      }
      goodsApi.goodsLike(params).then(res => {
     
            
          var goods=this.data.goods 
          goods.isLike=!goods.isLike 
          goods.like+=1
          this.setData({
            goods
          })
  
          wx.showToast({
            title: goods.isLike?'已点赞':'已取消点赞',
            icon: 'success'
          });
     
      }).catch(err => {
    
      });
    }else {

      goodsApi.deleteGoodsLike(this.data.goods.id).then(res => {
    
            
          var goods=this.data.goods 
          goods.isLike=!goods.isLike 
          goods.like-=1
          this.setData({
            goods
          })
  
          wx.showToast({
            title: goods.isLike?'已点赞':'已取消点赞',
            icon: 'success'
          });
    
      }).catch(err => {
      
      });


    }
    
   

  },

  // 分享商品
  shareGoods: function() {
    // 检查商品数据是否存在
    if (!this.data.goods) {
      wx.showToast({
        title: '商品信息加载失败',
        icon: 'none'
      });
      return;
    }

    // 使用微信自带的分享功能，不需要额外实现
  },

  // 复制联系信息
  copyContactInfo: function() {
    // 检查商品数据是否存在
    if (!this.data.goods) {
      wx.showToast({
        title: '商品信息加载失败',
        icon: 'none'
      });
      return;
    }

    // 复制联系信息到剪贴板
    wx.setClipboardData({
      data: this.data.goods.contactInfo,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        });
      }
    });
  },

  // 拨打电话
  callPhone: function() {
    // 检查商品数据是否存在
    if (!this.data.goods) {
      wx.showToast({
        title: '商品信息加载失败',
        icon: 'none'
      });
      return;
    }

    // 拨打电话
    wx.makePhoneCall({
      phoneNumber: this.data.goods.contactInfo,
      fail: (err) => {
        console.error('拨打电话失败:', err);
        wx.showToast({
          title: '拨打电话失败',
          icon: 'none'
        });
      }
    });
  },

  // 阻止事件冒泡
  stopPropagation: function() {
    // 阻止事件冒泡
    return false;
  },

  // 预览图片
  previewImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.goods.images;

    // 处理图片URL，添加服务器地址前缀
    const fullImageUrls = images.map(image => {
      if (image.startsWith('http')) {
        return image; // 已经是完整URL
      } else {
        return this.data.apiUrl + image;
      }
    });

    wx.previewImage({
      current: fullImageUrls[index],
      urls: fullImageUrls
    });
  },

  // 图片加载错误处理
  handleImageError: function(e) {
    const index = e.currentTarget.dataset.index;
    const defaultImage = 'https://img.freepik.com/free-photo/modern-stationary-collection-arrangement_23-2149309642.jpg';

    // 更新失败的图片为默认图片
    const images = this.data.goods.images;
    images[index] = defaultImage;

    this.setData({
      'goods.images': images
    });
  },

  // 增加购买数量
  increaseQuantity: function() {
    const { buyQuantity, goods } = this.data;

    // 检查库存
    if (buyQuantity >= goods.stock) {
      wx.showToast({
        title: `库存不足，当前库存仅剩${goods.stock}件`,
        icon: 'none',
        duration: 2000
      });
      return;
    }

    this.setData({
      buyQuantity: buyQuantity + 1
    });

    // 重新计算总价
    this.updateBuyModalPrice();
  },

  // 减少购买数量
  decreaseQuantity: function() {
    const { buyQuantity } = this.data;

    if (buyQuantity <= 1) {
      return;
    }

    this.setData({
      buyQuantity: buyQuantity - 1
    });

    // 重新计算总价
    this.updateBuyModalPrice();
  },

  // 输入购买数量
  onQuantityInput: function(e) {
    let quantity = parseInt(e.detail.value);

    // 检查是否为有效数字
    if (isNaN(quantity) || quantity < 1) {
      quantity = 1;
    }

    // 检查库存
    if (quantity > this.data.goods.stock) {
      quantity = this.data.goods.stock;
      wx.showToast({
        title: '已达最大库存',
        icon: 'none'
      });
    }

    this.setData({
      buyQuantity: quantity
    });

    // 重新计算总价
    this.updateBuyModalPrice();
  },

  // 输入备注
  onRemarkInput: function(e) {
    this.setData({
      buyRemark: e.detail.value
    });
  },

  // 积分滑块变化处理
  onPointSliderChange: function(e) {
    const points = parseInt(e.detail.value);
    this.setData({
      usePoints: points
    });

    // 计算积分抵扣金额
    this.calculatePointDiscount();

    // 计算总价
    this.calculateTotalPrice();

    // 更新预计可获得的积分
    this.calculateRewardPoints();
  },

  // 计算积分折扣
  calculatePointDiscount() {
    if (!this.data.usePoints || this.data.usePoints <= 0) {
      this.setData({
        pointDiscount: '0.00'
      });
      return;
    }

    const { goods, usePoints, buyQuantity } = this.data;
    if (!goods || !goods.pointsDiscount || !goods.pointsDiscount.enabled) {
      this.setData({
        pointDiscount: '0.00'
      });
      return;
    }

    // 根据积分比例计算折扣金额
    const ratio = goods.pointsDiscount.ratio || 100; // 默认100积分=1元
    let discount = usePoints / ratio;

    // 计算商品总价
    const totalGoodsPrice = goods.amount * buyQuantity;

    // 限制最大折扣比例
    const maxRatio = goods.pointsDiscount.maxRatio || 0.3; // 默认最多折扣30%
    const maxDiscount = totalGoodsPrice * maxRatio;
    if (discount > maxDiscount) {
      discount = maxDiscount;
    }

    // 保留两位小数
    discount = parseFloat(discount.toFixed(2));

    this.setData({
      pointDiscount: discount.toFixed(2)
    });

    // 更新总价
    this.calculateTotalPrice();
  },

  // 计算总价
  calculateTotalPrice() {
    const { goods, buyQuantity, pointDiscount } = this.data;
    if (!goods) return;

    // 确保 amount 是有效数字
    const amount = parseFloat(goods.amount) || 0;
    const quantity = parseInt(buyQuantity) || 1;

    console.log('计算总价:', { amount, quantity, goodsAmount: goods.amount });

    let totalPrice = amount * quantity;

    // 减去积分折扣
    if (pointDiscount && parseFloat(pointDiscount) > 0) {
      totalPrice -= parseFloat(pointDiscount);
      // 确保价格不低于0.01元
      if (totalPrice < 0.01) {
        totalPrice = 0.01;
      }
    }

    // 保留两位小数
    totalPrice = parseFloat(totalPrice.toFixed(2));

    this.setData({
      totalPrice: totalPrice.toFixed(2),
      actualPrice: totalPrice.toFixed(2)
    });

    // 更新预计可获得的积分
    this.calculateRewardPoints();
  },

  // 更新购买弹窗中的价格
  updateBuyModalPrice: function() {
    // 重新计算积分抵扣上限
    const { goods } = this.data;
    if (goods && goods.type === 1 && goods.pointsDiscount && goods.pointsDiscount.enabled) {
      this.calculateMaxPoints();
    }

    // 重新计算总价
    this.calculateTotalPrice();
  },

  // 计算预计可获得的积分
  calculateRewardPoints() {
    const { goods, totalPrice } = this.data;
    if (!goods || goods.type !== 1) return;

    // 确保商品有积分奖励字段，默认为1（1元奖励1积分）
    if (!goods.pointsReward) {
      // 如果商品没有积分奖励字段，设置一个默认值
      this.setData({
        'goods.pointsReward': 1
      });
    }

    // 从商品数据中获取积分奖励比例
    const rewardRatio = parseInt(goods.pointsReward) || 1;
    // 计算预计可获得的积分
    const rewardPoints = Math.floor(parseFloat(totalPrice) * rewardRatio);

    console.log('计算预计可获得的积分:', {
      totalPrice: totalPrice,
      rewardRatio: rewardRatio,
      rewardPoints: rewardPoints
    });

    this.setData({
      estimatedRewardPoints: rewardPoints,
      rewardPoints: rewardPoints
    });
  },

  // 关闭购买确认弹窗
  closeBuyModal: function() {
    this.setData({
      showBuyModal: false
    });
  },

  // 关闭联系方式弹窗
  closeContactModal: function() {
    this.setData({
      showContactModal: false
    });
  },

  // 确认购买 (Modal Confirm Button)
  confirmBuy: function() {
    const { goods, buyQuantity, buyRemark, totalPrice } = this.data;

    if (!goods) return; // Should not happen if modal is open

    // 最终库存检测
    if (buyQuantity > goods.stock) {
      wx.showToast({
        title: `库存不足，当前库存仅剩${goods.stock}件，请调整购买数量`,
        icon: 'none',
        duration: 3000
      });
      return;
    }

    if (goods.stock <= 0) {
      wx.showToast({
        title: '商品已售罄，无法购买',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 调试日志，查看商品ID存储位置
    console.log('商品对象结构:', {
      hasId: !!goods.id,
      hasUnderscoreId: !!goods._id,
      id: goods.id,
      _id: goods._id,
      goodsObj: goods
    });

    // 使用正确的ID字段（优先使用id，如果没有再尝试_id）
    const goodsId = goods.id || goods._id || goods.goodsId;

    if (!goodsId) {
      wx.showToast({
        title: '商品ID不存在',
        icon: 'none'
      });
      return;
    }

    // 准备创建订单的参数，根据API文档格式
    const order = {
      goodStuffId: goodsId,
      quantity: buyQuantity,
      amount: parseFloat(totalPrice),
      remark: buyRemark || '',
      // 其他字段根据实际API需要添加
    };

    wx.showLoading({ title: '正在创建订单...' });

    // 调用订单创建API
    goodsApi.createGoodsOrder(order)
      .then(res => {
        wx.hideLoading();
        this.setData({ showBuyModal: false });

      

          wx.showToast({
            title: '订单创建成功',
            icon: 'success'
          });

          // 如果有返还积分，显示提示
          if (this.data.estimatedRewardPoints > 0) {
            setTimeout(() => {
              wx.showToast({
                title: `获得${this.data.estimatedRewardPoints}积分奖励`,
                icon: 'success',
                duration: 1500
              });
            }, 1500);
          }
           
          // 跳转到订单详情页
          setTimeout(() => {
            wx.navigateTo({
              url: `/profilePackage/pages/goods/order/order?id=${res}&searchType=buyer`
            });
          }, 2500);
      
      })
      .catch(error => {
        wx.hideLoading();
   
      });
  },

  // 编辑商品
  editGoods: function() {
    const goods = this.data.goods;
    if (goods && goods.id) {
      wx.navigateTo({
        url: `/profilePackage/pages/goods/publish/publish?id=${goods.id}&mode=edit`
      });
    }
  },

  // 管理商品
  manageGoods: function() {
    const goods = this.data.goods;
    if (goods && goods.id) {
      wx.navigateTo({
        url: `/profilePackage/pages/goods/my/my?tab=published&goodsId=${goods.id}`
      });
    }
  },

  // 动态判断是否为免费类型（基于字典配置）
  checkIsFreeType: function(type, typeOptions) {
    const typeOption = typeOptions.find(option => option.nameEn === type);
    // 可以根据字典中的特殊标识来判断，比如 nameEn 包含 'free' 或者有特殊字段
    return typeOption && (typeOption.nameEn.includes('free') || typeOption.nameCn.includes('免费'));
  }
})
